import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/movie_model.dart';
import '../controllers/auth_controller.dart';

class FavoriteController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final RxList<Movie> favorites = <Movie>[].obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  late AuthController _authController;

  @override
  void onInit() {
    super.onInit();
    _authController = Get.find<AuthController>();
    loadFavorites();

    // Listen to auth changes to reload favorites when user logs in/out
    ever(_authController.isLoggedInObs, (_) {
      loadFavorites();
    });
  }

  Future<void> loadFavorites() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      favorites.clear();

      if (_authController.isLoggedIn) {
        // User is logged in, try to load from Firestore
        await _loadFromFirestore();
      } else {
        // User is not logged in, load from local storage
        await _loadFromLocalStorage();
      }
    } catch (e) {
      errorMessage.value = 'Failed to load favorites: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _loadFromFirestore() async {
    try {
      final userId = _authController.user?.id;
      if (userId == null) return;

      final snapshot = await _firestore
          .collection('favorites')
          .where('userId', isEqualTo: userId)
          .get();

      final List<Movie> loadedFavorites = [];
      for (var doc in snapshot.docs) {
        final data = doc.data();
        // Extract movie data from the favorite document
        final movieData = {
          'id': data['movieId'],
          'title': data['title'],
          'overview': data['overview'],
          'poster_path': data['posterPath'],
          'backdrop_path': data['backdropPath'],
          'release_date': data['releaseDate'],
          'vote_average': data['voteAverage'],
          'vote_count': data['voteCount'],
          'genres': data['genres'] ?? [],
          'original_title': data['originalTitle'],
          'popularity': data['popularity'],
          'language': data['language'],
          'runtime': data['runtime'],
          'age_rating': data['ageRating'],
          'director': data['director'],
          'status': data['status'],
        };
        loadedFavorites.add(Movie.fromJson(movieData, isFavorite: true));
      }

      favorites.value = loadedFavorites;
    } catch (e) {
      // If Firestore fails, try to load from local storage as fallback
      await _loadFromLocalStorage();
    }
  }

  Future<void> _loadFromLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getStringList('favorites') ?? [];

      final List<Movie> loadedFavorites = [];
      for (var json in favoritesJson) {
        final Map<String, dynamic> data = jsonDecode(json);
        loadedFavorites.add(Movie.fromJson(data, isFavorite: true));
      }

      favorites.value = loadedFavorites;
    } catch (e) {
      errorMessage.value = 'Failed to load favorites from local storage: $e';
    }
  }

  Future<void> toggleFavorite(Movie movie) async {
    try {
      final isFavorite = favorites.any((m) => m.id == movie.id);

      if (isFavorite) {
        // Remove from favorites
        favorites.removeWhere((m) => m.id == movie.id);
      } else {
        // Add to favorites
        favorites.add(movie.copyWith(isFavorite: true));
      }

      // Save changes
      if (_authController.isLoggedIn) {
        await _saveToFirestore();
      }
      await _saveToLocalStorage();
    } catch (e) {
      errorMessage.value = 'Failed to update favorites: $e';
    }
  }

  Future<void> _saveToFirestore() async {
    try {
      final userId = _authController.user?.id;
      if (userId == null) return;

      // Get current favorites in Firestore for this user
      final snapshot = await _firestore
          .collection('favorites')
          .where('userId', isEqualTo: userId)
          .get();

      // Create a batch to perform multiple operations
      final batch = _firestore.batch();

      // Delete all existing favorites for this user
      for (var doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      // Add all current favorites
      for (var movie in favorites) {
        final favoriteData = {
          'userId': userId,
          'movieId': movie.id,
          'title': movie.title,
          'overview': movie.overview,
          'posterPath': movie.posterPath,
          'backdropPath': movie.backdropPath,
          'releaseDate': movie.releaseDate,
          'voteAverage': movie.voteAverage,
          'voteCount': movie.voteCount,
          'genres': movie.genres,
          'originalTitle': movie.originalTitle,
          'popularity': movie.popularity,
          'language': movie.language,
          'runtime': movie.runtime,
          'ageRating': movie.ageRating,
          'director': movie.director,
          'status': movie.status.name,
          'createdAt': FieldValue.serverTimestamp(),
        };

        final docRef = _firestore.collection('favorites').doc();
        batch.set(docRef, favoriteData);
      }

      // Commit the batch
      await batch.commit();
    } catch (e) {
      // If Firestore fails, at least save to local storage
      errorMessage.value = 'Failed to save favorites to Firestore: $e';
    }
  }

  Future<void> _saveToLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String> favoritesJson =
          favorites.map((movie) => jsonEncode(movie.toJson())).toList();

      await prefs.setStringList('favorites', favoritesJson);
    } catch (e) {
      errorMessage.value = 'Failed to save favorites to local storage: $e';
    }
  }

  bool isFavorite(int movieId) {
    return favorites.any((movie) => movie.id == movieId);
  }
}
