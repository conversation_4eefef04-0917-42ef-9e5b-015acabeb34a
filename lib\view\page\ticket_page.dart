import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../controllers/ticket_controller.dart';
import '../../models/ticket_model.dart';

enum TicketFilterType {
  upcoming,
  past,
  cancelled,
}

class TicketPage extends StatefulWidget {
  const TicketPage({Key? key}) : super(key: key);

  @override
  State<TicketPage> createState() => _TicketPageState();
}

class _TicketPageState extends State<TicketPage> {
  late TicketController ticketController;

  @override
  void initState() {
    super.initState();
    // Ensure TicketController is initialized
    try {
      ticketController = Get.find<TicketController>();
    } catch (e) {
      ticketController = Get.put(TicketController());
    }

    // Force reload tickets when page is opened
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ticketController.loadTickets();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SafeArea(
        child: DefaultTabController(
          length: 3,
          child: Column(
            children: [
              // Header with statistics
              Container(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'Vé Của Tôi',
                          style: GoogleFonts.mulish(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: () {
                            ticketController.loadTickets();
                          },
                          icon: Obx(() => ticketController.isLoading.value
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                )
                              : const Icon(
                                  Icons.refresh,
                                  color: Colors.white,
                                )),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // Statistics
                    _buildStatistics(),
                  ],
                ),
              ),

              // Tab bar
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: TabBar(
                  indicator: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xff4E4376), Color(0xff2B5876)],
                    ),
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.white60,
                  labelStyle: GoogleFonts.mulish(
                    fontSize: 13,
                    fontWeight: FontWeight.w700,
                  ),
                  unselectedLabelStyle: GoogleFonts.mulish(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                  dividerColor: Colors.transparent,
                  tabs: [
                    Tab(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.schedule, size: 14),
                            const SizedBox(width: 2),
                            Flexible(
                              child: Text(
                                'Sắp Tới',
                                style: TextStyle(fontSize: 12),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 2),
                            Obx(() => Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 4, vertical: 1),
                                  decoration: BoxDecoration(
                                    color: Colors.green.withOpacity(0.8),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    '${ticketController.upcomingTickets.where((t) => t.status == TicketStatus.confirmed).length}',
                                    style: const TextStyle(
                                      fontSize: 9,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                )),
                          ],
                        ),
                      ),
                    ),
                    Tab(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.history, size: 14),
                            const SizedBox(width: 2),
                            const Flexible(
                              child: Text(
                                'Đã Qua',
                                style: TextStyle(fontSize: 12),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 2),
                            Obx(() => Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 4, vertical: 1),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withOpacity(0.8),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    '${ticketController.pastTickets.length}',
                                    style: const TextStyle(
                                      fontSize: 9,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                )),
                          ],
                        ),
                      ),
                    ),
                    Tab(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.cancel_outlined, size: 14),
                            const SizedBox(width: 2),
                            const Flexible(
                              child: Text(
                                'Đã Hủy',
                                style: TextStyle(fontSize: 12),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 2),
                            Obx(() => Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 4, vertical: 1),
                                  decoration: BoxDecoration(
                                    color: Colors.red.withOpacity(0.8),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    '${ticketController.tickets.where((t) => t.status == TicketStatus.cancelled).length}',
                                    style: const TextStyle(
                                      fontSize: 9,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                )),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Content
              Expanded(
                child: TabBarView(
                  children: [
                    _buildTicketList(filterType: TicketFilterType.upcoming),
                    _buildTicketList(filterType: TicketFilterType.past),
                    _buildTicketList(filterType: TicketFilterType.cancelled),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatistics() {
    return Obx(() {
      final totalTickets = ticketController.tickets.length;
      final confirmedTickets = ticketController.tickets
          .where((t) => t.status == TicketStatus.confirmed)
          .length;
      final cancelledTickets = ticketController.tickets
          .where((t) => t.status == TicketStatus.cancelled)
          .length;
      final totalSpent = ticketController.tickets
          .fold<double>(0.0, (sum, ticket) => sum + ticket.finalPrice);

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xff4E4376), Color(0xff2B5876)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Tổng Vé',
                    totalTickets.toString(),
                    Icons.confirmation_number,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatItem(
                    'Đã Chi',
                    NumberFormat.currency(locale: 'vi_VN', symbol: '₫')
                        .format(totalSpent),
                    Icons.attach_money,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Còn Hiệu Lực',
                    confirmedTickets.toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatItem(
                    'Đã Hủy',
                    cancelledTickets.toString(),
                    Icons.cancel,
                    Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget _buildStatItem(
      String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: GoogleFonts.mulish(
              fontSize: 12,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTicketList({required TicketFilterType filterType}) {
    return Obx(() {
      if (ticketController.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(
            color: Colors.blue,
          ),
        );
      }

      // Show error message if any
      if (ticketController.errorMessage.value.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 80,
                color: Colors.red.withOpacity(0.7),
              ),
              const SizedBox(height: 16),
              Text(
                'Có lỗi xảy ra',
                style: GoogleFonts.mulish(
                  fontSize: 18,
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                ticketController.errorMessage.value,
                style: GoogleFonts.mulish(
                  fontSize: 14,
                  color: Colors.white.withOpacity(0.5),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ticketController.loadTickets(),
                child: const Text('Thử lại'),
              ),
            ],
          ),
        );
      }

      List<Ticket> tickets;
      String emptyTitle;
      String emptySubtitle;
      IconData emptyIcon;
      bool showBookButton = false;

      switch (filterType) {
        case TicketFilterType.upcoming:
          tickets = ticketController.upcomingTickets
              .where((t) => t.status == TicketStatus.confirmed)
              .toList();
          emptyTitle = 'Chưa có vé sắp tới';
          emptySubtitle = 'Đặt vé xem phim đầu tiên của bạn!';
          emptyIcon = Icons.event_available;
          showBookButton = true;
          break;
        case TicketFilterType.past:
          tickets = ticketController.pastTickets;
          emptyTitle = 'Chưa có vé đã qua';
          emptySubtitle = 'Lịch sử vé sẽ hiển thị ở đây';
          emptyIcon = Icons.history;
          break;
        case TicketFilterType.cancelled:
          tickets = ticketController.tickets
              .where((t) => t.status == TicketStatus.cancelled)
              .toList();
          emptyTitle = 'Chưa có vé đã hủy';
          emptySubtitle = 'Các vé đã hủy sẽ hiển thị ở đây';
          emptyIcon = Icons.cancel_outlined;
          break;
      }

      print('Debug: ${filterType.name} tickets count: ${tickets.length}');
      print('Debug: All tickets count: ${ticketController.tickets.length}');

      if (tickets.isEmpty) {
        return Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  emptyIcon,
                  size: 60,
                  color: Colors.white.withOpacity(0.5),
                ),
                const SizedBox(height: 12),
                Text(
                  emptyTitle,
                  style: GoogleFonts.mulish(
                    fontSize: 16,
                    color: Colors.white.withOpacity(0.7),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  emptySubtitle,
                  style: GoogleFonts.mulish(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.5),
                  ),
                  textAlign: TextAlign.center,
                ),
                if (showBookButton) ...[
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      // Navigate to all movies page to book tickets
                      Get.toNamed('/all_movies', parameters: {
                        'genre': '', // Show all movies
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 10,
                      ),
                    ),
                    child: Text(
                      'Đặt vé ngay',
                      style: GoogleFonts.mulish(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: tickets.length,
        itemBuilder: (context, index) {
          final ticket = tickets[index];
          return _buildTicketCard(
              ticket, filterType == TicketFilterType.upcoming);
        },
      );
    });
  }

  Widget _buildTicketCard(Ticket ticket, bool isUpcoming) {
    final dateFormat = DateFormat('dd/MM/yyyy');
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      color: Colors.white.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: ticket.status == TicketStatus.cancelled
            ? BorderSide(color: Colors.red.withOpacity(0.5), width: 1)
            : BorderSide.none,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with movie title and status
            Row(
              children: [
                Expanded(
                  child: Text(
                    ticket.movieTitle,
                    style: GoogleFonts.mulish(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(ticket.status),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _getStatusText(ticket.status),
                    style: GoogleFonts.mulish(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Movie poster and details
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Movie poster
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: _buildPosterImage(ticket.moviePosterPath),
                ),

                // Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Date and time
                      Row(
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            size: 16,
                            color: Colors.white70,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${dateFormat.format(DateTime.parse(ticket.date))} • ${ticket.time}',
                            style: GoogleFonts.mulish(
                              fontSize: 14,
                              color: Colors.white70,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Theater and screen
                      Row(
                        children: [
                          const Icon(
                            Icons.location_on,
                            size: 16,
                            color: Colors.white70,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '${ticket.theaterName} • ${ticket.screenName}',
                              style: GoogleFonts.mulish(
                                fontSize: 14,
                                color: Colors.white70,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Seats
                      Row(
                        children: [
                          const Icon(
                            Icons.event_seat,
                            size: 16,
                            color: Colors.white70,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Ghế: ${_formatSeats(ticket.seats)}',
                            style: GoogleFonts.mulish(
                              fontSize: 14,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Price
                      Row(
                        children: [
                          const Icon(
                            Icons.attach_money,
                            size: 16,
                            color: Colors.white70,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            currencyFormat.format(ticket.finalPrice),
                            style: GoogleFonts.mulish(
                              fontSize: 16,
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Action button
                if (isUpcoming && ticket.status == TicketStatus.confirmed)
                  Column(
                    children: [
                      IconButton(
                        onPressed: () =>
                            _showCancelDialog(ticket, ticketController),
                        icon: const Icon(
                          Icons.cancel_outlined,
                          color: Colors.red,
                          size: 24,
                        ),
                        tooltip: 'Hủy vé',
                      ),
                    ],
                  ),
              ],
            ),

            const SizedBox(height: 12),

            // Booking code
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.white.withOpacity(0.1),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.qr_code,
                    size: 20,
                    color: Colors.white70,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Mã đặt vé: ',
                    style: GoogleFonts.mulish(
                      fontSize: 14,
                      color: Colors.white70,
                    ),
                  ),
                  Text(
                    ticket.bookingCode,
                    style: GoogleFonts.robotoMono(
                      fontSize: 14,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatSeats(List<TicketSeat> seats) {
    if (seats.isEmpty) return 'N/A';
    if (seats.length == 1) return seats.first.seatId;
    return '${seats.first.seatId} +${seats.length - 1}';
  }

  String _getStatusText(TicketStatus status) {
    switch (status) {
      case TicketStatus.confirmed:
        return 'Đã xác nhận';
      case TicketStatus.used:
        return 'Đã sử dụng';
      case TicketStatus.cancelled:
        return 'Đã hủy';
      case TicketStatus.expired:
        return 'Hết hạn';
    }
  }

  Color _getStatusColor(TicketStatus status) {
    switch (status) {
      case TicketStatus.confirmed:
        return Colors.green;
      case TicketStatus.used:
        return Colors.blue;
      case TicketStatus.cancelled:
        return Colors.red;
      case TicketStatus.expired:
        return Colors.orange;
    }
  }

  Widget _buildPosterImage(String? posterPath) {
    final posterUrl = _getCorrectPosterUrl(posterPath);

    if (posterUrl == null) {
      return Container(
        height: 100,
        width: 70,
        color: Colors.grey[800],
        child: const Center(
          child: Icon(
            Icons.movie,
            color: Colors.white54,
            size: 30,
          ),
        ),
      );
    }

    return Image.network(
      posterUrl,
      height: 100,
      width: 70,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          height: 100,
          width: 70,
          color: Colors.grey[800],
          child: const Center(
            child: Icon(
              Icons.movie,
              color: Colors.white54,
              size: 30,
            ),
          ),
        );
      },
    );
  }

  String? _getCorrectPosterUrl(String? posterPath) {
    if (posterPath == null || posterPath.isEmpty) {
      return null; // Return null to use errorBuilder
    }

    // If it's already a full URL (like YouTube thumbnail), use it directly
    if (posterPath.startsWith('http')) {
      return posterPath;
    }

    // If it's a TMDB path (starts with /), construct the full URL
    if (posterPath.startsWith('/')) {
      return 'https://image.tmdb.org/t/p/w200$posterPath';
    }

    // Fallback to null to use errorBuilder
    return null;
  }

  void _showCancelDialog(Ticket ticket, TicketController controller) {
    Get.dialog(
      AlertDialog(
        backgroundColor: const Color(0xff1a1a1a),
        title: Text(
          'Hủy Vé',
          style: GoogleFonts.mulish(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Bạn có chắc chắn muốn hủy vé này?\n\nPhim: ${ticket.movieTitle}\nNgày: ${ticket.date}\nGiờ: ${ticket.time}',
          style: GoogleFonts.mulish(
            color: Colors.white70,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'Không',
              style: GoogleFonts.mulish(
                color: Colors.white70,
              ),
            ),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              final success = await controller.cancelTicket(ticket.id);
              if (success) {
                Get.snackbar(
                  'Thành công',
                  'Vé đã được hủy thành công',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  'Lỗi',
                  controller.errorMessage.value,
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            child: Text(
              'Có, Hủy vé',
              style: GoogleFonts.mulish(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
