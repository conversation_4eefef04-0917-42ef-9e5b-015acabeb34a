import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../config/paypal_config.dart';

class PayPalService {
  // Process PayPal payment
  static Future<Map<String, dynamic>> processPayment({
    required double amount,
    required String currency,
    required String description,
    required BuildContext context,
  }) async {
    try {
      // Check if PayPal is configured
      if (!PayPalConfig.isConfigured) {
        return {
          'success': false,
          'error': 'PayPal is not configured. Please check your credentials.',
          'method': 'paypal',
        };
      }

      // For now, use simulation since PayPal SDK has compatibility issues
      // In production, you would integrate with PayPal REST API or use a working SDK

      // Show PayPal simulation dialog
      final result = await _showPayPalSimulationDialog(
          context, amount, currency, description);

      if (result == true) {
        return {
          'success': true,
          'paymentId': 'PAYPAL_SIM_${DateTime.now().millisecondsSinceEpoch}',
          'payerId': 'SIMULATED_PAYER',
          'token': 'SIMULATED_TOKEN',
          'amount': amount,
          'currency': currency,
          'method': 'paypal_simulation',
          'timestamp': DateTime.now().toIso8601String(),
          'description': description,
          'note': 'This is a simulated PayPal payment for testing purposes',
        };
      } else {
        return {
          'success': false,
          'error': 'Payment was cancelled by user',
          'method': 'paypal_simulation',
        };
      }
    } catch (e) {
      print('PayPal payment error: $e');
      return {
        'success': false,
        'error': e.toString(),
        'method': 'paypal',
      };
    }
  }

  // Validate PayPal configuration
  static bool isConfigured() {
    return PayPalConfig.isConfigured;
  }

  // Get PayPal environment info
  static Map<String, dynamic> getEnvironmentInfo() {
    return {
      'environment': PayPalConfig.environmentName,
      'clientId': PayPalConfig.isConfigured
          ? '${PayPalConfig.clientId.substring(0, 10)}...'
          : 'Not configured',
      'configured': PayPalConfig.isConfigured,
    };
  }

  // Convert VND to USD for PayPal (PayPal doesn't support VND)
  static double convertVndToUsd(double vndAmount) {
    // Exchange rate VND to USD (approximate, should be updated from real API)
    const double exchangeRate = 24000.0; // 1 USD = 24,000 VND (approximate)
    return vndAmount / exchangeRate;
  }

  // Format amount for display
  static String formatAmount(double amount, String currency) {
    if (currency == 'USD') {
      return '\$${amount.toStringAsFixed(2)}';
    } else if (currency == 'VND') {
      return '${amount.toStringAsFixed(0).replaceAllMapped(
            RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
            (Match m) => '${m[1]},',
          )} ₫';
    }
    return '${amount.toStringAsFixed(2)} $currency';
  }

  // Create payment description
  static String createPaymentDescription({
    required String movieTitle,
    required String theaterName,
    required String showtime,
    required List<String> seats,
  }) {
    return 'Movie: $movieTitle | Theater: $theaterName | Time: $showtime | Seats: ${seats.join(", ")}';
  }

  // Simulate payment for testing (when PayPal is not configured)
  static Future<Map<String, dynamic>> simulatePayment({
    required double amount,
    required String currency,
    required String description,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 2));

    return {
      'success': true,
      'paymentId': 'SIMULATED_${DateTime.now().millisecondsSinceEpoch}',
      'payerId': 'SIMULATED_PAYER',
      'token': 'SIMULATED_TOKEN',
      'amount': amount,
      'currency': currency,
      'method': 'paypal_simulation',
      'timestamp': DateTime.now().toIso8601String(),
      'description': description,
      'note': 'This is a simulated payment for testing purposes',
    };
  }

  // Show PayPal simulation dialog
  static Future<bool?> _showPayPalSimulationDialog(
    BuildContext context,
    double amount,
    String currency,
    String description,
  ) async {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.payment, color: Colors.blue),
            SizedBox(width: 8),
            Text('PayPal Payment'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Amount: ${formatAmount(amount, currency)}'),
            const SizedBox(height: 8),
            Text('Description: $description'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'PayPal Simulation',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'This is a simulated PayPal payment for testing purposes. In production, this would redirect to PayPal.',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Simulate processing delay
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const AlertDialog(
                  content: Row(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(width: 16),
                      Text('Processing payment...'),
                    ],
                  ),
                ),
              );

              await Future.delayed(const Duration(seconds: 2));
              Navigator.of(context).pop(); // Close loading dialog
              Navigator.of(context).pop(true); // Return success
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Pay with PayPal'),
          ),
        ],
      ),
    );
  }

  // Show PayPal configuration dialog for developers
  static void showConfigurationDialog(BuildContext context) {
    final envInfo = getEnvironmentInfo();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('PayPal Configuration'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Environment: ${envInfo['environment']}'),
            Text('Client ID: ${envInfo['clientId']}'),
            Text('Configured: ${envInfo['configured']}'),
            const SizedBox(height: 16),
            if (!envInfo['configured'])
              const Text(
                'PayPal is not properly configured. Please update the client ID and secret key in PayPalService.',
                style: TextStyle(color: Colors.red),
              ),
            if (envInfo['configured'])
              const Text(
                'PayPal is configured and ready to use.',
                style: TextStyle(color: Colors.green),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
